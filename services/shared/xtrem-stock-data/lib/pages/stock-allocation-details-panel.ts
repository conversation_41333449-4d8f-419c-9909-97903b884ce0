import { asyncArray } from '@sage/xtrem-async-helper';
import type { ExtractEdgesPartial, Filter, integer } from '@sage/xtrem-client';
import { extractEdges, withoutEdges } from '@sage/xtrem-client';
import type {
    BaseDocumentLine,
    Item,
    ItemSite,
    LocationBinding,
    Supplier,
    UnitOfMeasure,
} from '@sage/xtrem-master-data-api';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import type {
    AllocationUpdateAction,
    GraphApi,
    LotBinding,
    SerialNumber,
    StockStatusBinding,
} from '@sage/xtrem-stock-data-api';
import type { Site } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import type { PartialCollectionValueWithIds } from '@sage/xtrem-ui/build/lib/component/types';
import type * as StockDataInterfaces from '../client-functions/interfaces';
import * as StockPillColor from '../client-functions/pill-color';
import { serialNumberRangesToSerialNumbers } from '../client-functions/serial-number';
import { queryAllocation } from '../client-functions/stock-allocation';
import * as StockAllocationDetailsPanelHelper from '../client-functions/stock-allocation-details-panel-helper';
import * as StockDetailHelper from '../client-functions/stock-details-helper';
import * as StockDataUtils from '../client-functions/utils';
import * as serialNumberHelper from '../shared-functions/serial-number-helper';

@ui.decorators.page<StockAllocationDetailsPanel>({
    title: 'Detailed stock allocation',
    module: 'inventory',
    mode: 'default',
    isTransient: true,
    access: { node: '@sage/xtrem-stock-data/StockAllocation', bind: '$lookup' },

    businessActions() {
        return [this.cancel, this.save];
    },
    async onLoad() {
        await this.init();
    },
    onError(error: string | (Error & { errors: Array<any> })) {
        return MasterDataUtils.formatError(this, error);
    },
})
export class StockAllocationDetailsPanel extends ui.Page<GraphApi> {
    validationPromise: Promise<void> | null;

    allocationValue: PartialCollectionValueWithIds<StockDataInterfaces.StockAllocationDetailPageBinding>[];

    queryParams: StockDataInterfaces.StockAllocationDetailPanelParameters;

    stockDetailId: string;

    stockRecordId: string;

    itemSiteInfo: ExtractEdgesPartial<ItemSite> | null;

    validateButtonDisablementLogic() {
        if (
            this.queryParams.isReturningDetails &&
            this.allocations.value.every(line => line.quantityToAllocate === 0) &&
            this.queryParams.jsonStockDetails?.length !== 0
        ) {
            this.save.isDisabled = false;
            return;
        }

        let isDirty = false;
        let serialNumbersSelected = true;

        if (this.$.isDirty) {
            let dirtyLines = 0;

            this.allocations.value.forEach(allocation => {
                if (
                    serialNumbersSelected &&
                    this.item.value?.serialNumberManagement === 'managed' &&
                    this.item.value?.serialNumberUsage === 'issueAndReceipt' &&
                    allocation.serialNumberStatus !== 'assigned' &&
                    (allocation.quantityToAllocate ?? 0) > 0
                ) {
                    serialNumbersSelected = false;
                }
                if (allocation.allocationPersistedData) {
                    if (
                        allocation.quantityToAllocate !== allocation.allocationPersistedData.quantityToAllocate ||
                        allocation.jsonSerialNumbers !== allocation.allocationPersistedData.jsonSerialNumbers
                    ) {
                        dirtyLines += 1;
                    }
                } else if ((allocation.quantityToAllocate ?? 0) > 0) {
                    dirtyLines += 1;
                }
            });
            if (dirtyLines > 0) {
                isDirty = true;
            }
        }

        if (
            !isDirty &&
            this.queryParams.needAllocationProposal &&
            this.allocations.selectedRecords.length > 0 &&
            (!this.queryParams.needFullAllocation || this.remainingQuantity.value === 0)
        ) {
            isDirty = true;
        }

        this.save.isDisabled =
            (this.allocatedQuantity.value || 0) < 0 ||
            (this.queryParams.needFullAllocation && this.remainingQuantity.value !== 0) ||
            !this.queryParams.isEditable ||
            !isDirty ||
            !serialNumbersSelected ||
            (this.queryParams.cannotOverAllocate && (this.remainingQuantity.value || 0) < 0);
    }

    @ui.decorators.referenceField<StockAllocationDetailsPanel>({
        isTransient: true,
        node: '@sage/xtrem-stock-data/StockStatus',
        tunnelPage: '@sage/xtrem-stock-data/StockStatus',
        valueField: 'name',
        lookupDialogTitle: 'Select quality value',
        minLookupCharacters: 0,
    })
    status: ui.fields.Reference<StockStatusBinding>;

    @ui.decorators.referenceField<StockAllocationDetailsPanel>({
        node: '@sage/xtrem-stock-data/Lot',
        valueField: 'id',
        lookupDialogTitle: 'Select lot',
    })
    lot: ui.fields.Reference<LotBinding>;

    @ui.decorators.section<StockAllocationDetailsPanel>({
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<StockAllocationDetailsPanel>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        title: 'General',
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.referenceField<StockAllocationDetailsPanel, Site>({
        parent() {
            return this.mainBlock;
        },
        title: 'Stock site',
        node: '@sage/xtrem-system/Site',
        tunnelPage: '@sage/xtrem-master-data/Site',
        lookupDialogTitle: 'Select site',
        minLookupCharacters: 0,
        valueField: 'name',
        helperTextField: 'id',
        isDisabled: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.text({ bind: { defaultLocation: { id: true } } }),
            ui.nestedFields.text({ bind: { defaultLocation: { name: true } } }),
            ui.nestedFields.checkbox({
                bind: 'isLocationManaged',
                title: 'Location management',
            }),
        ],
    })
    site: ui.fields.Reference<Site>;

    @ui.decorators.referenceField<StockAllocationDetailsPanel, Item>({
        parent() {
            return this.mainBlock;
        },
        title: 'Item',
        node: '@sage/xtrem-master-data/Item',
        tunnelPage: '@sage/xtrem-master-data/Item',
        lookupDialogTitle: 'Select item',
        valueField: 'name',
        helperTextField: 'id',
        isDisabled: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.text({ bind: 'description' }),
            ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
        ],
    })
    item: ui.fields.Reference<Item>;

    @ui.decorators.referenceField<StockAllocationDetailsPanel, UnitOfMeasure>({
        parent() {
            return this.mainBlock;
        },
        title: 'Stock unit',
        node: '@sage/xtrem-master-data/UnitOfMeasure',
        tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
        lookupDialogTitle: 'Select unit of measure',
        minLookupCharacters: 1,
        valueField: 'name',
        helperTextField: 'symbol',
        isDisabled: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.technical({ bind: 'id' }),
            ui.nestedFields.text({ bind: 'symbol' }),
            ui.nestedFields.technical({ bind: 'description' }),
        ],
    })
    unit: ui.fields.Reference<UnitOfMeasure>;

    @ui.decorators.numericField<StockAllocationDetailsPanel>({
        parent() {
            return this.mainBlock;
        },
        title: 'Required quantity',
        isDisabled: true,
        scale() {
            return MasterDataUtils.getScaleValue(0, this.unit.value?.decimalDigits);
        },
    })
    quantity: ui.fields.Numeric;

    @ui.decorators.numericField<StockAllocationDetailsPanel>({
        parent() {
            return this.mainBlock;
        },
        title: 'Selected quantity',
        isDisabled: true,
        isHidden() {
            return !this.queryParams.isEditable;
        },
        scale() {
            return MasterDataUtils.getScaleValue(0, this.unit.value?.decimalDigits);
        },
    })
    allocatedQuantity: ui.fields.Numeric;

    @ui.decorators.numericField<StockAllocationDetailsPanel>({
        parent() {
            return this.mainBlock;
        },
        title: 'Remaining quantity',
        isDisabled: true,
        isHidden() {
            return !this.queryParams.isEditable;
        },
        scale() {
            return MasterDataUtils.getScaleValue(0, this.unit.value?.decimalDigits);
        },
    })
    remainingQuantity: ui.fields.Numeric;

    @ui.decorators.referenceField<StockAllocationDetailsPanel>({
        node: '@sage/xtrem-master-data/Location',
        title: 'Location',
        bind: { location: { id: true } },
        columns: [ui.nestedFields.technical({ bind: 'id' }), ui.nestedFields.technical({ bind: 'name' })],
        valueField: 'name',
        isHidden: true,
        parent() {
            return this.mainBlock;
        },
    })
    location: ui.fields.Reference<LocationBinding>;

    @ui.decorators.referenceField<StockAllocationDetailsPanel, LocationBinding>({
        parent() {
            return this.mainBlock;
        },
        title: 'Default location',
        node: '@sage/xtrem-master-data/Location',
        columns: [ui.nestedFields.technical({ bind: 'id' }), ui.nestedFields.technical({ bind: 'name' })],
        valueField: 'name',
        isDisabled: true,
    })
    defaultLocation: ui.fields.Reference<LocationBinding>;

    @ui.decorators.separatorField<StockAllocationDetailsPanel>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    separator: ui.fields.Separator;

    @ui.decorators.dropdownListField<StockAllocationDetailsPanel>({
        parent() {
            return this.mainBlock;
        },
        title: 'Select location',
        optionType: '@sage/xtrem-stock-data/LocationFilterType',
        onChange() {
            this.filterLocation();
        },
    })
    locationFilter: ui.fields.DropdownList;

    @ui.decorators.tableField<StockAllocationDetailsPanel>({
        parent() {
            return this.mainSection;
        },
        title: 'Stock allocation',
        isTransient: true,
        canSelect: true,
        pageSize: 10,
        orderBy: {
            displaySort: +1,
            stockId: +1,
        },
        columns: [
            ui.nestedFields.technical({ bind: 'displaySort' }),
            ui.nestedFields.technical({ bind: 'stockId' }),
            ui.nestedFields.technical({ bind: 'allocationId' }),
            ui.nestedFields.numeric({
                title: 'Quantity to allocate',
                bind: 'quantityToAllocate',
                unit: (_rowId, rowData) => rowData?.stockUnit,
                unitMode: 'unitOfMeasure',
                validation(this, val, rowData) {
                    if (val < 0) {
                        return ui.localize(
                            '@sage/xtrem-stock-data/value-cannot-be-negative',
                            'This value cannot be negative.',
                        );
                    }
                    if (this.isUnderAllocated(val, rowData) && this.queryParams.needFullAllocation) {
                        return ui.localize(
                            '@sage/xtrem-stock-data/full-allocation-needed',
                            'A full allocation is needed.',
                        );
                    }
                    if (this.isOverAllocated(val, rowData) && this.queryParams.cannotOverAllocate) {
                        return ui.localize(
                            '@sage/xtrem-stock-data/cannot-over-allocate',
                            'The allocation cannot exceed the required quantity.',
                        );
                    }
                    return undefined;
                },
                async onChange(name, value) {
                    await StockDetailHelper.allocationAndIssueLineOnUserChangeQuantityField(this, {
                        validationPromiseName: 'validationPromise',

                        tableField: this.allocations,

                        onChangeId: name,
                        onChangeValue: value,

                        lineSelectedQuantityFieldName: 'quantityToAllocate',

                        movementType: 'allocation',
                    });

                    if (
                        value.jsonSerialNumberRanges &&
                        value.jsonSerialNumberRanges !== '{}' &&
                        this.item.value?.serialNumberUsage === 'issueAndReceipt' &&
                        +value.quantityToAllocate !== 0 &&
                        value.quantityToAllocate !== JSON.parse(value.jsonSerialNumbers as string).length
                    ) {
                        this.$.showToast(
                            ui.localize(
                                '@sage/xtrem-stock-data/pages__stock_allocation_detail_panel_quantity_changed',
                                'The quantity to allocate changed. Update the serial numbers.',
                            ),
                            { timeout: 5000, type: 'warning' },
                        );
                    }
                    this.updateSerialNumberPropertiesForLine(
                        this.allocations.selectedRecords.includes(value._id),
                        value.jsonSerialNumberRanges,
                    );
                },
            }),
            ui.nestedFields.numeric({
                title: 'Available quantity',
                bind: 'quantityAvailable',
                isDisabled: true,
                isHidden() {
                    return (
                        !!this.queryParams?.fieldCustomizations?.quantityAvailable?.isHidden ||
                        !this.queryParams.isEditable
                    );
                },
                unit: (_rowId, rowData) => rowData?.stockUnit,
                unitMode: 'unitOfMeasure',
            }),
            ui.nestedFields.reference<StockAllocationDetailsPanel>({
                title: 'Quality control',
                bind: { status: { name: true } },
                node: '@sage/xtrem-stock-data/StockStatus',
                valueField: 'name',
                isReadOnly: true,
            }),
            ui.nestedFields.technical<StockAllocationDetailsPanel>({
                bind: 'stockUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                ],
            }),
            ui.nestedFields.reference<StockAllocationDetailsPanel>({
                title: 'Location',
                node: '@sage/xtrem-master-data/Location',
                valueField: 'id',
                bind: 'location',
                isReadOnly: true,
            }),
            ui.nestedFields.reference<StockAllocationDetailsPanel>({
                title: 'Lot',
                bind: 'lot',
                node: '@sage/xtrem-stock-data/Lot',
                valueField: 'id',
                isDisabled: true,
                isHidden() {
                    return (
                        this.item.value?.lotManagement !== 'lotManagement' &&
                        this.item.value?.lotManagement !== 'lotSublotManagement'
                    );
                },
            }),
            ui.nestedFields.reference<StockAllocationDetailsPanel>({
                title: 'Sublot',
                bind: { lot: { sublot: true } },
                node: '@sage/xtrem-stock-data/Lot',
                valueField: 'sublot',
                isReadOnly: true,
                isHidden() {
                    return (
                        this.item.value?.lotManagement !== 'lotManagement' &&
                        this.item.value?.lotManagement !== 'lotSublotManagement'
                    );
                },
            }),
            ui.nestedFields.date({
                title: 'Expiration date',
                bind: 'expirationDate',
                isDisabled: true,
                isHidden() {
                    return !!this.item.value?.isExpiryManaged;
                },
            }),
            ui.nestedFields.label<StockAllocationDetailsPanel>({
                title: 'Serial number',
                bind: 'serialNumberStatus',
                isHidden() {
                    return !(
                        this.item.value?.serialNumberManagement === 'managed' &&
                        this.item.value?.serialNumberUsage === 'issueAndReceipt'
                    );
                },
                optionType: '@sage/xtrem-stock-data/StockDetailSerialNumberStatus',
                style: (_id, rowData) =>
                    StockPillColor.getLabelColorByStatus('StockDetailSerialNumberStatus', rowData.serialNumberStatus),
            }),
            ui.nestedFields.technical({ bind: 'quantityFound' }),
            ui.nestedFields.technical<StockAllocationDetailsPanel>({
                bind: 'orderDocumentLine',
                node: '@sage/xtrem-master-data/BaseDocumentLine',
            }),
            ui.nestedFields.technical({ bind: 'quantityAllocatedOnOrderDocument' }),
            ui.nestedFields.technical({ bind: 'quantityTransferred' }),
            ui.nestedFields.technical({ isTransientInput: true, bind: 'jsonSerialNumbers' }),
            ui.nestedFields.technical({ isTransientInput: true, bind: 'jsonSerialNumberRanges' }),
        ],
        /* FIXME:
         * We cannot update header fields from the grid
         * https://jira.sage.com/browse/XT-678
         */
        async onChange() {
            await StockDataUtils.awaitPreviousValidationPromise(this, this.validationPromise, 'validationPromise');

            this.allocationQuantityManager();

            this.validationPromise = (async () => {
                await StockDetailHelper.checkReadiness(this);
            })();
        },
        async onRowSelected(_id: string, detail: StockDataInterfaces.StockAllocationDetailPageBinding) {
            await StockDataUtils.awaitPreviousValidationPromise(this, this.validationPromise, 'validationPromise');

            this.allocationQuantityManager({ changedLineId: _id, changedLineHasBeenSelected: true });

            // We need to read the record again as the quantityToAllocate may have changed
            const relatedStockDetailLine = this.allocations.getRecordValue(_id);
            detail.quantityToAllocate = relatedStockDetailLine?.quantityToAllocate;
            this.loadSerialNumberRangesForStockDetailLine(true, detail);
            this.updateSerialNumberPropertiesForLine(true, '{}');

            this.validationPromise = (async () => {
                await StockDetailHelper.checkReadiness(this);
            })();
        },
        async onRowUnselected(_id: string, detail: StockDataInterfaces.StockAllocationDetailPageBinding) {
            await StockDataUtils.awaitPreviousValidationPromise(this, this.validationPromise, 'validationPromise');

            this.allocationQuantityManager({ changedLineId: _id });

            this.loadSerialNumberRangesForStockDetailLine(false, detail);
            this.updateSerialNumberPropertiesForLine(false, '{}');

            this.validationPromise = (async () => {
                await StockDetailHelper.checkReadiness(this);
            })();
        },
        onRowClick(_id: string, rowData: StockDataInterfaces.StockAllocationDetailPageBinding) {
            this.loadSerialNumberRangesForStockDetailLine(
                this.allocations.selectedRecords.includes(rowData._id),
                rowData,
            );
        },
    })
    allocations: ui.fields.Table<StockDataInterfaces.StockAllocationDetailPageBinding>; // <StockAllocationDetailsPanelLine>;

    @ui.decorators.tableField<StockAllocationDetailsPanel>({
        title: 'Serial numbers',
        pageSize: 10,
        orderBy: {
            startingSerialNumber: +1,
        },
        canSelect: false,
        isTransient: true,
        isDisabled() {
            return (
                !this.queryParams.isEditable ||
                (this.item.value?.serialNumberManagement === 'managed' &&
                    this.item.value?.serialNumberUsage !== 'issueAndReceipt')
            );
        },
        columns: [
            ui.nestedFields.reference<
                StockAllocationDetailsPanel,
                StockDataInterfaces.StockIssueDetailSerialNumberPageBinding,
                NonNullable<StockDataInterfaces.StockIssueDetailSerialNumberPageBinding['startingSerialNumber']>
            >({
                title: 'From serial number',
                bind: 'startingSerialNumber',
                node: '@sage/xtrem-stock-data/SerialNumber',
                valueField: 'id',
                minLookupCharacters: 0,
                lookupDialogTitle: 'Select from serial number',
                columns: [
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.reference<StockAllocationDetailsPanel, SerialNumber, Supplier>({
                        bind: 'supplier',
                        title: 'Supplier',
                        node: '@sage/xtrem-master-data/Supplier',
                        valueField: { businessEntity: { name: true } },
                        helperTextField: { businessEntity: { id: true } },
                        columns: [
                            ui.nestedFields.text({ bind: { businessEntity: { name: true } }, title: 'Name' }),
                            ui.nestedFields.text({ bind: { businessEntity: { id: true } }, title: 'ID' }),
                        ],
                    }),
                ],
                filter() {
                    const orderDocumentLineFilter: Array<string> = [];
                    if (this.queryParams.documentLine) {
                        orderDocumentLineFilter.push(this.queryParams.documentLine);
                    }
                    if (this.queryParams.orderDocumentLine) {
                        orderDocumentLineFilter.push(this.queryParams.orderDocumentLine);
                    }
                    return {
                        stockRecord: { _id: { _eq: `${this.stockRecordId}` } },
                        isUsable: true,
                        isInStock: true,
                        _or: [
                            { isAllocated: false },
                            { allocation: { documentLine: { _id: { _in: orderDocumentLineFilter } } } },
                        ],
                    };
                },
                validation(_val, rowData) {
                    if (
                        StockAllocationDetailsPanelHelper.initSerialNumberRowData(rowData) &&
                        !this.checkUniquenessOfSerialNumberRange(rowData)
                    ) {
                        return ui.localize(
                            '@sage/xtrem-stock-data/pages__stock_allocation_detail_panel_duplicate_error',
                            'The serial number is already included in another range.',
                        );
                    }
                    const line = this.allocationSerialNumbers.getRecordValue(rowData._id);
                    // enforce setRecordValue if startingSerialNumber really changed. Reason:
                    // 'validation' for quantity property is triggered before onChange
                    // of startingSerialNumber property. Therefore the quantity is in this case the old one before
                    // change of startingSerialNumber and we enforce the new quantity here (didn't find another solution).
                    if (line?.originalStartId !== rowData.startingSerialNumber?.id || '')
                        this.allocationSerialNumbers.setRecordValue(rowData);
                    return undefined;
                },
                onChange(_id: number, rowData: StockDataInterfaces.StockIssueDetailSerialNumberPageBinding) {
                    if (StockAllocationDetailsPanelHelper.initSerialNumberRowData(rowData)) {
                        rowData.originalStartId = rowData.startingSerialNumber?.id || '';
                        this.allocationSerialNumbers.setRecordValue(rowData);
                        this.updateSerialNumberPropertiesForLine(
                            true,
                            JSON.stringify(this.allocationSerialNumbers.value),
                        );
                    }
                },
            }),
            ui.nestedFields.numeric<StockAllocationDetailsPanel>({
                title: 'Quantity',
                bind: 'quantity',
                async validation(val: number, rowData: StockDataInterfaces.StockIssueDetailSerialNumberPageBinding) {
                    // Prevent a quantity of zero
                    if (val <= 0)
                        return ui.localize(
                            '@sage/xtrem-stock-data/pages__stock_allocation_detail_panel_zero_quantity_error',
                            'Enter a quantity greater than 0.',
                        );
                    if (StockAllocationDetailsPanelHelper.prepareSerialNumberRowData(rowData)) {
                        // check in the database if all serial numbers within the calculated range are available
                        const orderDocumentLineFilter: Array<string> = [];
                        if (this.queryParams.documentLine) {
                            orderDocumentLineFilter.push(this.queryParams.documentLine);
                        }
                        if (this.queryParams.orderDocumentLine) {
                            orderDocumentLineFilter.push(this.queryParams.orderDocumentLine);
                        }

                        const filter: Filter<SerialNumber> = {
                            stockRecord: { _id: { _eq: `${this.stockRecordId}` } },
                            isUsable: true,
                            isInStock: true,
                            _or: [
                                { isAllocated: false },
                                { allocation: { documentLine: { _id: { _in: orderDocumentLineFilter } } } },
                            ],
                            _and: [
                                { id: { _gte: rowData.startingSerialNumber?.id || '' } },
                                { id: { _lte: rowData.endingSerialNumber?.id || '' } },
                            ],
                        };

                        const searchRange = extractEdges(
                            await this.$.graph
                                .node('@sage/xtrem-stock-data/SerialNumber')
                                .query(
                                    ui.queryUtils.edgesSelector(
                                        { _id: true, id: true },
                                        {
                                            filter,
                                            orderBy: {
                                                id: 1,
                                            },
                                            first: 1000,
                                        },
                                    ),
                                )
                                .execute(),
                        );

                        // enforce re-reading line from grid. Reason:
                        // 'validation' is triggered when startingSerialNumber has been changed before onChange
                        // of startingSerialNumber. Therefore the quantity is in this case the old one before
                        // change of startingSerialNumber. In 'validation' of startingSerialNumber we therefore
                        // already update the quantity to 1 (as no other solution found) in the grid
                        const line = this.allocationSerialNumbers.getRecordValue(rowData._id);

                        // check if the last serial number of the range is the same as the calculated last one
                        // (without gaps) of the range. If not > calculate available range
                        const serialNumberNumericPart = parseInt(
                            serialNumberHelper.splitSerialNumber(searchRange[searchRange.length - 1].id)
                                .serialNumberNumericPart,
                            10,
                        );
                        if (
                            searchRange.length > 0 &&
                            (searchRange.length !== line?.quantity || serialNumberNumericPart !== rowData.numericEnd)
                        ) {
                            let gapFound = false;
                            let rangeLength: integer = 0;
                            let actNumeric: integer = 0;
                            // find the first gap
                            searchRange.forEach(serialNumber => {
                                const numericPart = parseInt(
                                    serialNumberHelper.splitSerialNumber(serialNumber.id).serialNumberNumericPart,
                                    10,
                                );
                                if (!gapFound && (rangeLength === 0 || actNumeric === numericPart - 1)) {
                                    rangeLength += 1;
                                } else gapFound = true;
                                actNumeric = numericPart;
                            });
                            if (!gapFound && rangeLength !== line?.quantity) {
                                gapFound = true;
                            }

                            // if not all serial numbers of range are available -> error
                            if (gapFound) {
                                return ui.localize(
                                    '@sage/xtrem-stock-data/pages__stock_allocation_detail_panel_available_error',
                                    'Serial numbers available in this range: {{available}}',
                                    { available: rangeLength },
                                );
                            }
                        }
                        // check the uniqueness of the calculated range for the stock detail
                        if (!this.checkUniquenessOfSerialNumberRange(rowData)) {
                            return ui.localize(
                                '@sage/xtrem-stock-data/pages__stock_allocation_detail_panel_duplicate_range_error',
                                'One of the serial numbers is already included in another range.',
                            );
                        }
                    }
                    return undefined;
                },
                onChange(_id: number, rowData: StockDataInterfaces.StockIssueDetailSerialNumberPageBinding) {
                    if (StockAllocationDetailsPanelHelper.prepareSerialNumberRowData(rowData)) {
                        this.allocationSerialNumbers.setRecordValue(rowData);
                        this.updateSerialNumberPropertiesForLine(
                            true,
                            JSON.stringify(this.allocationSerialNumbers.value),
                        );
                    }
                },
            }),
            ui.nestedFields.reference({
                title: 'To serial number',
                bind: 'endingSerialNumber',
                node: '@sage/xtrem-stock-data/SerialNumber',
                valueField: 'id',
                isReadOnly: true,
                columns: [
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.reference({
                        bind: 'supplier',
                        title: 'Supplier',
                        node: '@sage/xtrem-master-data/Supplier',
                        valueField: { businessEntity: { name: true } },
                        helperTextField: { businessEntity: { id: true } },
                        columns: [
                            ui.nestedFields.text({ bind: { businessEntity: { name: true } }, title: 'Name' }),
                            ui.nestedFields.text({ bind: { businessEntity: { id: true } }, title: 'ID' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'numericStart' }),
            ui.nestedFields.technical({ bind: 'numericEnd' }),
            ui.nestedFields.technical({ bind: 'originalStartId' }),
        ],
        dropdownActions: [
            {
                icon: 'bin',
                title: 'Delete',
                isDestructive: true,
                isDisabled() {
                    return !this.queryParams.isEditable;
                },
                async onClick(rowId) {
                    await StockDataUtils.awaitPreviousValidationPromise(
                        this,
                        this.validationPromise,
                        'validationPromise',
                    );

                    this.allocationSerialNumbers.removeRecord(rowId);
                    this.updateSerialNumberPropertiesForLine(true, JSON.stringify(this.allocationSerialNumbers.value));

                    await this.refreshAllocationJsonSerialNumbers();

                    this.validationPromise = (async () => {
                        await StockDetailHelper.checkReadiness(this);
                    })();
                },
            },
        ],
        fieldActions() {
            return [this.addSerialNumberRange];
        },
        async onChange() {
            await StockDataUtils.awaitPreviousValidationPromise(this, this.validationPromise, 'validationPromise');

            await this.refreshAllocationJsonSerialNumbers();

            this.validationPromise = (async () => {
                await StockDetailHelper.checkReadiness(this);
            })();
        },
        parent() {
            return this.mainSection;
        },
    })
    allocationSerialNumbers: ui.fields.Table;

    private async refreshAllocationJsonSerialNumbers() {
        const detail = this.allocations.value.find(item => item.stockId === this.stockRecordId);
        if (detail) {
            const orderDocumentLineFilter: Array<string> = [];
            if (this.queryParams.documentLine) {
                orderDocumentLineFilter.push(this.queryParams.documentLine);
            }
            if (this.queryParams.orderDocumentLine) {
                orderDocumentLineFilter.push(this.queryParams.orderDocumentLine);
            }

            detail.jsonSerialNumbers = JSON.stringify(
                await serialNumberRangesToSerialNumbers(this, {
                    serialNumberRanges: this.allocationSerialNumbers.value,
                    serialNumberInfo: { allocation: { _id: true } },
                    serialNumberFilter: {
                        stockRecord: { _id: { _eq: `${detail.stockId}` } },
                        isUsable: true,
                        isInStock: true,
                        _or: [
                            { isAllocated: false },
                            {
                                allocation: {
                                    documentLine: {
                                        _id: { _in: orderDocumentLineFilter },
                                    },
                                },
                            },
                        ],
                    },
                }),
            );
            if (detail.allocationPersistedData?.jsonSerialNumbers !== detail.jsonSerialNumbers)
                detail.isPersisted = false;
            this.allocations.setRecordValue(detail);
        }
    }

    @ui.decorators.pageAction<StockAllocationDetailsPanel>({
        icon: 'add',
        title: 'Add line',
        onClick() {
            this.allocationSerialNumbers.addRecord({});
        },
    })
    addSerialNumberRange: ui.PageAction;

    @ui.decorators.pageAction<StockAllocationDetailsPanel>({
        title: 'Save',
        async onClick() {
            this.$.loader.isHidden = false;

            // Doesn't a allow to issue the same stock for Sales Shipment and Purchase Returns
            // Doesn't work on Material tracking because material tracking page creates a new document line id on stock detail
            if (
                this.queryParams.documentLine &&
                (await this.$.graph
                    .node('@sage/xtrem-stock-data/StockMovementDetail')
                    .queries.haveStockDetail(true, { documentLine: this.queryParams.documentLine })
                    .execute())
            ) {
                throw new Error(
                    ui.localize(
                        '@sage/xtrem-stock-data/pages__stock_issue_details_panel__document_line_already_issued',
                        'The document line was issued.',
                    ),
                );
            }

            this.allocationQuantityManager();

            if (
                this.item.value?.serialNumberManagement === 'managed' &&
                this.item.value?.serialNumberUsage === 'issueAndReceipt'
            ) {
                const details: ui.PartialNodeWithId<StockDataInterfaces.StockAllocationDetailPageBinding>[] =
                    this.allocations.value.filter(
                        stockDetail => stockDetail.quantityToAllocate && stockDetail.quantityToAllocate > 0,
                    );
                if (!this.controlSerialNumberData(details)) {
                    this.$.loader.isHidden = true;
                    return;
                }
            }

            let allocationUpdates: StockDataInterfaces.ManufacturingAllocationUpdates = [];

            this.allocations.value.forEach(allocation => {
                const additionalProperties: { stockRecord?: string; quantityProposed?: number } = {};
                if (this.queryParams.isReturningDetails) {
                    additionalProperties.stockRecord = allocation.allocationPersistedData?.stockRecord.toString();
                    additionalProperties.quantityProposed = allocation.allocationPersistedData?.quantityProposed;
                }

                let checkSerialNumbers =
                    this.item.value?.serialNumberManagement === 'managed' &&
                    this.item.value?.serialNumberUsage === 'issueAndReceipt';
                let check = false;
                if (this.queryParams.isReturningDetails) {
                    check = true;
                } else if (this.allocations.selectedRecords.includes(allocation._id)) {
                    check = (allocation.quantityToAllocate || 0) > 0;
                    if (allocation.allocationPersistedData) {
                        check =
                            check &&
                            allocation.allocationPersistedData.quantityToAllocate !== allocation.quantityToAllocate;
                    }
                } else if (
                    allocation.allocationId &&
                    allocation.allocationPersistedData &&
                    allocation.allocationPersistedData.quantityToAllocate !== 0
                ) {
                    checkSerialNumbers = false;
                    allocationUpdates.unshift({
                        ...additionalProperties,
                        action: 'delete',
                        stockRecord: allocation.stockId,
                        allocationRecord: allocation.allocationId,
                    });
                }

                const getQuantityDelta = (persistedQuantityToAllocate: number) =>
                    (allocation.quantityToAllocate || 0) - persistedQuantityToAllocate;

                let snDelta: string[] = [];
                let snToReject: string[] = [];
                if (checkSerialNumbers) {
                    const serialNumbersAllocation = allocation.jsonSerialNumbers
                        ? (JSON.parse(allocation.jsonSerialNumbers as string) as SerialNumber[]).map(sn => sn._id)
                        : [];

                    if (allocation.allocationPersistedData?.jsonSerialNumbers) {
                        const serialNumbersPersisted = (
                            JSON.parse(allocation.allocationPersistedData.jsonSerialNumbers as string) as SerialNumber[]
                        ).map(sn => sn._id);

                        if (this.queryParams.isReturningDetails) {
                            snDelta = serialNumbersAllocation;
                        } else {
                            serialNumbersAllocation.forEach(sn => {
                                if (serialNumbersPersisted.indexOf(sn) === -1) {
                                    snDelta.push(sn);
                                }
                            });
                        }
                        serialNumbersPersisted.forEach(sn => {
                            if (serialNumbersAllocation.indexOf(sn) === -1) {
                                if (this.queryParams.isReturningDetails && this.queryParams.orderDocumentLine) {
                                    snToReject.push(sn);
                                } else snDelta.push(sn);
                            }
                        });
                    } else snDelta = serialNumbersAllocation;
                }

                // split snDelta into snToChange and snToTransfer
                // snTransfer = snDelta with sn allocated on order document
                const snToTransfer =
                    this.queryParams.orderDocumentLine && snDelta.length
                        ? (JSON.parse(allocation.jsonSerialNumbers as string) as SerialNumber[])
                              .filter(sn => {
                                  const snDeltaAllocated = snDelta.includes(sn._id) && sn.allocation;
                                  if (snDeltaAllocated && allocation.orderAllocationId === undefined) {
                                      allocation.orderAllocationId = sn.allocation._id;
                                  }
                                  return snDeltaAllocated;
                              })
                              .map(sn => sn._id)
                        : [];
                if (snToTransfer.length > 0 && snToReject.length > 0) {
                    const reduceNumberOfReject =
                        snToReject.length -
                        (allocation.allocationPersistedData?.quantityProposed || 0) +
                        snToTransfer.length;
                    if (reduceNumberOfReject > 0) snToReject = snToReject.slice(0, -reduceNumberOfReject);
                }
                const snToChange = snDelta.filter(sn => !snToTransfer.includes(sn));

                if (check) {
                    // stock line is selected with a qty to allocate
                    // it is a new allocation or an existing one with qty to allocate to change
                    const persistedQuantityToAllocate = Number(
                        allocation.allocationPersistedData?.quantityToAllocate || 0,
                    );
                    const userDecreasedAllocationQuantity =
                        (allocation.quantityToAllocate || 0) < persistedQuantityToAllocate;

                    if (
                        allocation.allocationId &&
                        allocation.allocationPersistedData &&
                        (!allocation.orderDocumentLine || userDecreasedAllocationQuantity)
                    ) {
                        if (userDecreasedAllocationQuantity) {
                            allocationUpdates.push({
                                ...additionalProperties,
                                action: 'decrease',
                                stockRecord: allocation.stockId,
                                allocationRecord: allocation.allocationId,
                                quantity: getQuantityDelta(persistedQuantityToAllocate).toString(),
                                serialNumbers: snDelta,
                            });
                        } else {
                            allocationUpdates.push({
                                ...additionalProperties,
                                action: 'increase',
                                stockRecord: allocation.stockId,
                                allocationRecord: allocation.allocationId,
                                quantity: getQuantityDelta(persistedQuantityToAllocate).toString(),
                                serialNumbers: snDelta,
                            });
                        }
                    } else {
                        let quantity = allocation.quantityToAllocate || 0;
                        if (allocation.orderDocumentLine) {
                            const orderedAllocationUpdates: StockDataInterfaces.ManufacturingAllocationUpdates = [];
                            if (allocation.allocationId) {
                                const deltaWithIssueAllocation = getQuantityDelta(persistedQuantityToAllocate);
                                const deltaWithOrderAllocation =
                                    (allocation.quantityAllocatedOnOrderDocument || 0) - deltaWithIssueAllocation;

                                if (deltaWithOrderAllocation < 0) {
                                    quantity = deltaWithIssueAllocation + deltaWithOrderAllocation;
                                    const quantityToAllocateFromAvailableStock =
                                        (allocation.quantityToAllocate || 0) -
                                        (quantity + (allocation.allocationPersistedData?.quantityToAllocate || 0));
                                    orderedAllocationUpdates.push({
                                        ...additionalProperties,
                                        action: 'increase',
                                        stockRecord: allocation.stockId,
                                        allocationRecord: allocation.allocationId,
                                        quantity: quantityToAllocateFromAvailableStock.toString(),
                                        serialNumbers: snDelta,
                                    });
                                } else {
                                    quantity = deltaWithIssueAllocation;
                                }
                            } else {
                                const quantityToReject = checkSerialNumbers
                                    ? snToReject.length
                                    : (allocation.allocationPersistedData?.quantityProposed || 0) -
                                      (allocation.quantityToAllocate || 0);
                                const quantityToCreate = checkSerialNumbers ? snToChange.length : -quantityToReject;

                                if (
                                    allocation.allocationPersistedData &&
                                    allocation.allocationPersistedData.allocationId &&
                                    this.queryParams.isReturningDetails &&
                                    allocation.allocationPersistedData.quantityProposed &&
                                    quantityToReject > 0
                                ) {
                                    orderedAllocationUpdates.unshift({
                                        ...additionalProperties,
                                        action: 'reject',
                                        allocationRecord: allocation.allocationPersistedData.allocationId,
                                        quantity: quantityToReject.toString(),
                                        serialNumbers: snToReject,
                                    });
                                }

                                if (
                                    allocation.allocationPersistedData &&
                                    allocation.allocationPersistedData.allocationId &&
                                    this.queryParams.isReturningDetails &&
                                    quantityToCreate > 0
                                ) {
                                    orderedAllocationUpdates.push({
                                        ...additionalProperties,
                                        action: 'create',
                                        stockRecord: allocation.stockId,
                                        quantity: quantityToCreate.toString(),
                                        serialNumbers: snToChange,
                                    });
                                    quantity -= quantityToCreate;
                                }
                            }

                            /**
                             * Example for not enough to transfer
                             *  order allocated : 4
                             * shipment allocated : 6
                             * stock available : infinite
                             *
                             * if user ask for 12 on shipment
                             * deltaWithIssueAllocation = 12 - 6 = 6
                             * deltaWithOrderAllocation = 4 - 6 = -2
                             * allocationData.quantity = 6 + -2 = 4
                             * quantityToAllocateFromAvailableStock = 12 - (6 + 6 + -2) = 12 - 10 = 2
                             *
                             * => transfer : 4
                             * => increase : 2
                             * ----------------------------------------------------------------------------
                             *
                             * Example for plenty to transfer
                             * order allocated : 13
                             * shipment allocated : 5
                             * stock available : infinite
                             *
                             * if user ask for 11 on shipment
                             * deltaWithIssueAllocation = 11 - 5 = 6
                             * deltaWithOrderAllocation = 13 - 6 = 5
                             * allocationData.quantity = 6
                             * => transfer : 6
                             * => increase : 0
                             *
                             */

                            if (quantity > 0) {
                                orderedAllocationUpdates.unshift({
                                    ...additionalProperties,
                                    action: 'transfer',
                                    stockRecord: allocation.stockId,
                                    allocationRecord: allocation.orderAllocationId,
                                    quantityToTransfer: quantity.toString(),
                                    serialNumbers: snToTransfer,
                                });
                            }
                            allocationUpdates.push(...orderedAllocationUpdates);
                        } else if (quantity > 0) {
                            allocationUpdates.push({
                                ...additionalProperties,
                                action: 'create',
                                stockRecord: allocation.stockId,
                                quantity: quantity.toString(),
                                serialNumbers: snDelta,
                            });
                        }
                    }
                } else {
                    // same quantity to allocate but serial numbers are to change
                    const quantity = snToTransfer.length;
                    if (quantity) {
                        allocationUpdates.push({
                            ...additionalProperties,
                            action: 'transfer',
                            stockRecord: allocation.stockId,
                            allocationRecord: allocation.orderAllocationId,
                            quantityToTransfer: quantity,
                            serialNumbers: snToTransfer,
                        });
                    }
                    if (snToChange.length) {
                        allocationUpdates.push({
                            ...additionalProperties,
                            action: 'increase',
                            stockRecord: allocation.stockId,
                            allocationRecord: allocation.allocationId,
                            quantity: -quantity,
                            serialNumbers: snToChange,
                        });
                    }
                }
            });

            if (this.queryParams.isReturningDetails) {
                // For a document line with allocation status = not allocated,
                // user can't guess if there is no allocation or allocations only with rejection.
                // As allocation proposal is executed only in the first case, user will not understand why proposal is not executed in the 2nd case.
                // Simplest solution is to assimilate these 2 cases in 1 in order to execute allocation proposal when document line is with allocation status = not allocated
                // It means user can do an order allocation rejection if he allocates something else instead,
                // otherwise he can go to the order document to deallocate it.
                if (allocationUpdates.every(allocation => allocation.action === 'reject')) allocationUpdates = [];
                else {
                    let deltaAllocated = allocationUpdates.reduce(
                        (prev, update) => {
                            let sign = 0;
                            if (['create', 'increase'].includes(update.action)) {
                                sign = 1;
                            } else if (['reject', 'decrease'].includes(update.action)) {
                                sign = -1;
                            }
                            return prev + Number(update.quantity || 0) * sign;
                        },
                        -(this.queryParams.orderRemainingQuantityToAllocate || 0),
                    );
                    if (deltaAllocated < 0) {
                        allocationUpdates = allocationUpdates.reduce(
                            (prev, allocation) => {
                                let updateToRemove = false;
                                if (allocation.action === 'reject') {
                                    const rejectQuantity = Number(allocation.quantity);
                                    if (rejectQuantity <= -deltaAllocated) {
                                        deltaAllocated += rejectQuantity;
                                        updateToRemove = true;
                                    } else if (deltaAllocated < 0 && rejectQuantity > -deltaAllocated) {
                                        allocation.quantity = rejectQuantity + deltaAllocated;
                                        if (allocation.serialNumbers) {
                                            allocation.serialNumbers = allocation.serialNumbers.slice(
                                                0,
                                                deltaAllocated,
                                            );
                                        }
                                        deltaAllocated = 0;
                                    }
                                }
                                if (!updateToRemove) {
                                    prev.push(allocation);
                                }
                                return prev;
                            },
                            [] as typeof allocationUpdates,
                        );
                    }
                }
                this.$.loader.isHidden = true;
                this.$.finish({ output: allocationUpdates });
            } else {
                const allocationUpdateResult = (await this.$.graph
                    .node('@sage/xtrem-stock-data/Stock')
                    .mutations.updateAllocations(
                        {
                            resultAction: true,
                            allocationRecord: {
                                _id: true,
                            },
                        },
                        {
                            allocationData: {
                                documentLine: Number(this.queryParams.documentLine),
                                allocationUpdates: StockDetailHelper.cleanUpAllocationUpdatesForMutation(
                                    allocationUpdates as StockDataInterfaces.AllocationUpdates,
                                ),
                            },
                            stockAllocationParameters: {
                                cannotOverAllocate: this.queryParams.cannotOverAllocate,
                                shouldControlAllocationInProgress: this.queryParams.shouldControlAllocationInProgress,
                            },
                        },
                    )
                    .execute()) as Array<{ resultAction: string; allocationRecord: { _id: string } }>;
                this.$.loader.isHidden = true;

                this.$.finish({ output: allocationUpdateResult.length > 0 });
            }
        },
    })
    save: ui.PageAction;

    // Do all the needed controls on OK of the Stock allocation details panel
    private controlSerialNumberData(
        details: ui.PartialNodeWithId<StockDataInterfaces.StockAllocationDetailPageBinding>[],
    ): boolean {
        let check = true;
        details.forEach(detail => {
            // check if the total quantity of assigned serial numbers fits the quantity of the stock detail
            if (detail.serialNumberStatus !== 'assigned') {
                check = false;
                this.$.showToast(
                    ui.localize(
                        '@sage/xtrem-stock-data/pages__stock_allocation_detail_panel_quantity_mismatch',
                        'Assign all serial numbers for this stock record. Number to assign: {{quantityToAllocate}}.',
                        {
                            quantityToAllocate: detail.quantityToAllocate,
                        },
                    ),
                    { timeout: 5000, type: 'error' },
                );
            }

            // check for overlapping ranges or serial numbers assigned more than once
            this.allocationSerialNumbers.value = [];
            if (detail.jsonSerialNumberRanges && detail.jsonSerialNumberRanges !== '{}') {
                const serialNumberRanges: ExtractEdgesPartial<StockDataInterfaces.StockIssueDetailSerialNumberPageBinding>[] =
                    JSON.parse(detail.jsonSerialNumberRanges as string);
                serialNumberRanges.forEach(serialNumberRange => {
                    this.allocationSerialNumbers.addOrUpdateRecordValue(serialNumberRange);
                });
                if (
                    !this.checkUniquenessOfSerialNumberRange(
                        this.allocationSerialNumbers
                            .value[0] as StockDataInterfaces.StockIssueDetailSerialNumberPageBinding,
                    )
                ) {
                    check = false;
                    this.$.showToast(
                        ui.localize(
                            '@sage/xtrem-stock-data/pages__stock_allocation_detail_panel_duplicate_range_error',
                            'One of the serial numbers is already included in another range.',
                        ),
                        { timeout: 5000, type: 'error' },
                    );
                    // we force the serial number status to 'partiallySelected'. This status is used later on the main
                    // page to set the stock detail status to 'required' even when the total quantity to allocate
                    // matches the quantity of the stock issue line in the document
                    detail.serialNumberStatus = 'partiallyAssigned';
                    this.allocations.addOrUpdateRecordValue(detail);
                }
            }
        });
        return check;
    }

    @ui.decorators.pageAction<StockAllocationDetailsPanel>({
        title: 'Cancel',
        buttonType: 'tertiary',
        onClick() {
            this.$.finish({ output: false });
        },
    })
    cancel: ui.PageAction;

    private async init() {
        this.loadQueryParams(this.$.queryParameters.args);
        await this.setTransientValues(this.queryParams);

        this.locationFilter.value = this.defaultLocation.value ? 'defaultLocation' : 'allLocations';
        this.locationFilter.isHidden = !this.defaultLocation.value;
        this.defaultLocation.isHidden = !this.defaultLocation.value;

        // All selectable stock records must be retrieved to match the allocations
        this.loadStockSearchResult();

        let linesToBeSelectedOnInit = await this.loadExistingAllocation();
        this.allocationQuantityManager({ isCalledInInitContext: true, linesToBeSelectedOnInit });

        this.allocationValue = this.allocations.value;

        if (this.queryParams.isEditable) {
            linesToBeSelectedOnInit = await this.loadCreationAllocations(
                linesToBeSelectedOnInit,
                this.queryParams.jsonStockDetails,
            );
            this.allocationQuantityManager({ isCalledInInitContext: true, linesToBeSelectedOnInit });
        } else this.allocations.canSelect = false;
        await this.convertSerialNumberData();

        await StockDetailHelper.checkReadiness(this);

        this.filterLocation();
        this.$.setPageClean();
    }

    private allocationQuantityManager(
        args: {
            isCalledInInitContext?: boolean;
            changedLineId?: string;
            changedLineHasBeenSelected?: boolean;
            linesToBeSelectedOnInit?: Array<string>;
        } = {},
    ) {
        StockDetailHelper.allocationAndIssueQuantityManager(this, {
            ...args,

            tableField: this.allocations,

            selectedQuantityField: this.allocatedQuantity,
            requiredQuantityField: this.quantity,
            remainingQuantityField: this.remainingQuantity,

            movementType: 'allocation',
            lineSelectedQuantityFieldName: 'quantityToAllocate',
            lineAvailableQuantityFieldName: 'quantityAvailable',
            lineFoundQuantityFieldName: 'quantityFound',
        });
    }

    // convert the json single serial number array to json serial number range array
    // the [SN0001, SN0002, SN0003] array is converted into [from SN0001 to SN0003] array
    private async convertSerialNumberData() {
        this.allocationSerialNumbers.isHidden = true;
        if (
            this.item.value?.serialNumberManagement === 'managed' &&
            this.item.value?.serialNumberUsage === 'issueAndReceipt'
        ) {
            const details: ui.PartialNodeWithId<StockDataInterfaces.StockAllocationDetailPageBinding>[] =
                this.allocations.value.filter(detail => detail.quantityToAllocate && detail.quantityToAllocate > 0);
            await asyncArray(details).forEach(detail => {
                this.allocationSerialNumbers.value = [];
                detail.serialNumberStatus = 'toBeAssigned';
                this.allocations.setRecordValue(detail);
                if (detail.jsonSerialNumbers) {
                    const serialNumbers: SerialNumber[] = JSON.parse(detail.jsonSerialNumbers as string);
                    const serialNumberIds = serialNumbers.map(serialNumber => serialNumber.id);
                    const ranges = serialNumberHelper.getSerialNumberRangesFromIds(serialNumberIds).map(range => {
                        const startingSerialNumber = serialNumbers.find(serial => serial.id === range.originalStartId);
                        const endingSerialNumber = serialNumbers.find(serial => serial.id === range.endId);
                        return {
                            startingSerialNumber,
                            quantity: range.quantity,
                            endingSerialNumber,
                            numericStart: range.numericStart,
                            numericEnd: range.numericEnd,
                            originalStartId: range.originalStartId,
                        } as StockDataInterfaces.StockIssueDetailSerialNumberPageBinding;
                    });
                    ranges.forEach(range => {
                        this.allocationSerialNumbers.addOrUpdateRecordValue(range);
                    });
                    this.stockDetailId = detail._id;
                    this.updateSerialNumberPropertiesForLine(true, JSON.stringify(this.allocationSerialNumbers.value));
                    this.allocationSerialNumbers.value = [];
                }
            });
        }
    }

    // Checks for the range of a changed row in the serial number grid if there are overlapping with another row of the grid
    private checkUniquenessOfSerialNumberRange(
        rowData: StockDataInterfaces.StockIssueDetailSerialNumberPageBinding,
    ): boolean {
        return !this.allocationSerialNumbers.value.some(
            serialNumber =>
                rowData._id !== serialNumber._id && // don't check actual row itself
                // check if prefix and postfix are identical
                serialNumberHelper.splitSerialNumber(rowData.startingSerialNumber?.id || '')
                    .serialNumberPrefixStringPart ===
                    serialNumberHelper.splitSerialNumber(serialNumber.startingSerialNumber.id || '')
                        .serialNumberPrefixStringPart &&
                serialNumberHelper.splitSerialNumber(rowData.startingSerialNumber?.id || '')
                    .serialNumberPostfixStringPart ===
                    serialNumberHelper.splitSerialNumber(serialNumber.startingSerialNumber.id || '')
                        .serialNumberPostfixStringPart &&
                // check ranges
                ((rowData.numericStart >= serialNumber.numericStart &&
                    rowData.numericStart <= serialNumber.numericEnd) ||
                    (rowData.numericEnd >= serialNumber.numericStart &&
                        rowData.numericEnd <= serialNumber.numericEnd) ||
                    (serialNumber.numericStart >= rowData.numericStart &&
                        serialNumber.numericStart <= rowData.numericEnd) ||
                    (serialNumber.numericEnd >= rowData.numericStart && serialNumber.numericEnd <= rowData.numericEnd)),
        );
    }

    private loadQueryParams(panelArgs: string | number | boolean) {
        if (panelArgs && typeof panelArgs === 'string') {
            const parameters =
                MasterDataUtils.tryParseJSON<StockDataInterfaces.StockAllocationDetailPanelParameters>(panelArgs);

            if (parameters) {
                this.queryParams = parameters;
            }
        }

        // default the isReturningDetails value to false
        if (this.queryParams.isReturningDetails === null) {
            this.queryParams.isReturningDetails = false;
        }

        // default the isEditable value to false
        if (this.queryParams.isEditable === null) {
            this.queryParams.isEditable = false;
        }

        // default the currentDetails value to an empty object
        if (this.queryParams.jsonStockDetails === null) {
            this.queryParams.jsonStockDetails = [];
        }

        // default the needFullAllocation value to false
        if (this.queryParams.needFullAllocation === null) {
            this.queryParams.needFullAllocation = false;
        }

        // default the cannotOverAllocate value to false
        if (this.queryParams.cannotOverAllocate === null || this.queryParams.cannotOverAllocate === undefined) {
            this.queryParams.cannotOverAllocate = false;
        }

        // default the shouldControlAllocationInProgress value to true
        this.queryParams.shouldControlAllocationInProgress = !!this.queryParams.shouldControlAllocationInProgress;

        // default the orderDocumentLine value to an empty object
        if (this.queryParams.orderDocumentLine === null) {
            this.queryParams.orderDocumentLine = '';
        }

        // default the needAllocationProposal value to false
        if (this.queryParams.needAllocationProposal === null || this.queryParams.needAllocationProposal === undefined) {
            this.queryParams.needAllocationProposal = false;
        }

        if (
            this.queryParams.orderRemainingQuantityToAllocate === null ||
            this.queryParams.orderRemainingQuantityToAllocate === undefined ||
            this.queryParams.orderRemainingQuantityToAllocate < 0
        ) {
            this.queryParams.orderRemainingQuantityToAllocate = 0;
        }
    }

    /**
     *  set transient fields : save quantity item site status
     * @param queryParams
     */
    private async setTransientValues(queryParams: this['queryParams']) {
        this.save.title = queryParams.isReturningDetails
            ? ui.localize('@sage/xtrem-stock-data/ok', 'Allocate')
            : ui.localize('@sage/xtrem-stock-data/save', 'Save');
        this.quantity.value = queryParams.quantity ?? null;
        if (queryParams.item) {
            this.item.value = await StockDataUtils.getItemInfo(this, queryParams.item);
        }
        if (queryParams.stockSite) {
            this.site.value = await StockDataUtils.getSiteInfo(this, queryParams.stockSite);
        }

        this.status.value = queryParams.stockStatus
            ? await StockDataUtils.getStatusInfo(this, queryParams.stockStatus)
            : null;
        if (queryParams.unit) {
            await StockDataUtils.setUnitInfo(this, this.unit, this.item, queryParams.unit, !!queryParams.unit);
        }
        if (queryParams.location) {
            await StockDataUtils.setLocationInfo(this, this.location, queryParams.location, !!queryParams.location);
        }
        if (queryParams.lot) {
            await StockDataUtils.setLotInfo(this, this.lot, queryParams.lot, !!queryParams.lot);
        }
        if (queryParams.item && queryParams.stockSite) {
            this.itemSiteInfo = await StockDataUtils.getItemSiteInfo(this, queryParams.stockSite, queryParams.item);
        }

        if (this.itemSiteInfo?.outboundDefaultLocation !== null) {
            this.defaultLocation.value = this.itemSiteInfo?.outboundDefaultLocation ?? null;
        } else {
            this.defaultLocation.value = this.site.value?.defaultLocation ?? null;
        }
    }

    private loadStockSearchResult() {
        if (
            this.queryParams.stockAllocationStockRecordsFound &&
            this.queryParams.stockAllocationStockRecordsFound.length > 0
        ) {
            this.queryParams.stockAllocationStockRecordsFound.forEach(stockRecord => {
                this.allocations.value = this.allocations.value.concat([
                    {
                        stockId: stockRecord.stockId,
                        allocationId: 0,
                        documentLine: { _id: this.queryParams.documentLine },
                        ...stockRecord,
                        _id: stockRecord._id ? stockRecord._id : this.allocations.generateRecordId(),
                        expirationDate: stockRecord.lot?.expirationDate,
                        quantityToAllocate: 0,
                        orderDocumentLine: null,
                        quantityAllocatedOnOrderDocument: 0,
                        quantityTransferred: 0,
                        // using type any to allow usage of Partial type here
                    } as unknown as PartialCollectionValueWithIds<StockDataInterfaces.StockAllocationDetailPageBinding>,
                ]);
            });
        }
    }

    /**
     * Loads the allocations grid if we have already stock detail entry information
     */
    private async loadExistingAllocation() {
        const linesToBeSelectedOnInit = new Array<string>();

        const allocations = await queryAllocation(this, !!this.queryParams.needAllocationProposal);

        if (allocations && allocations.length > 0) {
            const allocationMappedWithStockSearchResult: Array<
                StockDataInterfaces.StockAllocationDetailPageBinding & { index: number }
            > = [];
            let otherStockRecords = this.allocations
                .value as unknown as Array<StockDataInterfaces.StockAllocationDetailPageBinding>;

            const concatOrderAndIssueAllocations = (
                allocationA: (StockDataInterfaces.StockAllocationDetailPageBinding & { index: number }) | null,
                allocationB: StockDataInterfaces.StockAllocationDetailPageBinding & { quantityToTransfer?: string },
                mappingArray: Array<StockDataInterfaces.StockAllocationDetailPageBinding & { index: number }>,
            ) => {
                const isAllocationBFromOrder = allocationB.documentLine?._id === this.queryParams.orderDocumentLine;
                const quantityAllocatedOnOrderDocument = isAllocationBFromOrder ? allocationB.quantityInStockUnit : 0;
                const quantityAllocatedOnIssueDocument = isAllocationBFromOrder
                    ? allocationA?.quantityInStockUnit || 0
                    : allocationB.quantityInStockUnit;

                if (!isAllocationBFromOrder || Number(allocationB.quantityToTransfer) > 0) {
                    linesToBeSelectedOnInit.push(allocationB._id);
                }

                if (allocationA) {
                    let orderDocumentLine;
                    if (isAllocationBFromOrder) {
                        orderDocumentLine = Number(allocationA.quantityTransferred)
                            ? allocationA.orderDocumentLine
                            : ({ _id: this.queryParams.orderDocumentLine } as unknown as BaseDocumentLine);
                    } else {
                        orderDocumentLine = allocationB.documentLine;
                    }
                    mappingArray[allocationA.index] = {
                        ...allocationA,
                        quantityToAllocate: Number(quantityAllocatedOnIssueDocument),
                        quantityAllocatedOnOrderDocument:
                            Number(quantityAllocatedOnOrderDocument) +
                            Number(allocationA.quantityAllocatedOnOrderDocument),
                        quantityTransferred: String(
                            Number(allocationA.quantityTransferred) + Number(allocationB.quantityTransferred),
                        ),
                        orderAllocationId: isAllocationBFromOrder
                            ? allocationB.allocationId
                            : allocationA.orderAllocationId,
                        allocationPersistedData: isAllocationBFromOrder
                            ? allocationA.allocationPersistedData
                            : allocationB.allocationPersistedData,
                        allocationId: isAllocationBFromOrder ? allocationA.allocationId : allocationB.allocationId,
                        documentLine: isAllocationBFromOrder ? allocationA.documentLine : allocationB.documentLine,
                        orderDocumentLine,
                        quantityInStockUnit: isAllocationBFromOrder
                            ? allocationA.quantityInStockUnit
                            : allocationB.quantityInStockUnit,
                        quantityFound: allocationA.quantityFound,
                        jsonSerialNumbers: isAllocationBFromOrder
                            ? allocationA.jsonSerialNumbers
                            : allocationB.jsonSerialNumbers,
                    };
                } else {
                    mappingArray.push({
                        ...allocationB,
                        index: mappingArray.length,
                        quantityToAllocate: isAllocationBFromOrder
                            ? Number(allocationB.quantityToTransfer) || 0
                            : Number(allocationB.quantityInStockUnit),
                        quantityAllocatedOnOrderDocument: Number(quantityAllocatedOnOrderDocument),
                        orderAllocationId: isAllocationBFromOrder ? allocationB.allocationId : undefined,
                        allocationId: isAllocationBFromOrder ? '0' : allocationB.allocationId,
                        documentLine: isAllocationBFromOrder
                            ? (undefined as unknown as BaseDocumentLine)
                            : allocationB.documentLine,
                        allocationPersistedData: {
                            allocationId: String(allocationB.allocationPersistedData?.allocationId),
                            stockRecord: String(allocationB.allocationPersistedData?.stockRecord),
                            quantityToAllocate: isAllocationBFromOrder
                                ? 0
                                : allocationB.allocationPersistedData?.quantityToAllocate || 0,
                            quantityProposed:
                                this.queryParams.needAllocationProposal && isAllocationBFromOrder
                                    ? allocationB.allocationPersistedData?.quantityToAllocate
                                    : undefined,
                            jsonSerialNumbers: allocationB.allocationPersistedData?.jsonSerialNumbers,
                        },
                        orderDocumentLine: Number(allocationB.quantityTransferred)
                            ? allocationB.orderDocumentLine
                            : ({ _id: this.queryParams.orderDocumentLine } as unknown as BaseDocumentLine),
                    });
                }
            };

            // 1st loop : maps allocation(s) with a stock record result on the grid
            // From the issue document line, or from the order document line
            allocations.forEach(allocation => {
                const correspondingGridRow = this.allocations.value.find(
                    stockSearchResult => `${stockSearchResult.stockId}` === allocation.stockRecord?._id,
                );

                if (correspondingGridRow) {
                    if (otherStockRecords.length !== 0) {
                        otherStockRecords = [];
                    }

                    const correspondingOrderOrIssueAllocation = allocationMappedWithStockSearchResult.find(
                        mapped => mapped.stockId === correspondingGridRow.stockId,
                    );

                    if (this.queryParams.isReturningDetails && !this.queryParams.needAllocationProposal) {
                        concatOrderAndIssueAllocations(
                            correspondingOrderOrIssueAllocation || null,
                            {
                                ...(correspondingGridRow as unknown as StockDataInterfaces.StockAllocationDetailPageBinding),
                                quantityFound: Number(correspondingGridRow.quantityFound),
                            },
                            allocationMappedWithStockSearchResult,
                        );
                    } else
                        concatOrderAndIssueAllocations(
                            correspondingOrderOrIssueAllocation || null,
                            {
                                ...(correspondingGridRow as unknown as StockDataInterfaces.StockAllocationDetailPageBinding),
                                stockId: correspondingGridRow.stockId,
                                documentLine: allocation.documentLine as unknown as BaseDocumentLine,
                                quantityInStockUnit: String(allocation.quantityInStockUnit),
                                quantityToTransfer: allocation.quantityToTransfer,
                                quantityFound: Number(correspondingGridRow.quantityFound),
                                allocationPersistedData: {
                                    allocationId: String(allocation?._id),
                                    stockRecord: String(allocation.stockRecord?._id),
                                    quantityToAllocate: this.queryParams.needAllocationProposal
                                        ? Number(allocation.quantityToTransfer)
                                        : Number(allocation.quantityInStockUnit),
                                    jsonSerialNumbers: allocation.serialNumbers
                                        ? JSON.stringify(allocation.serialNumbers)
                                        : '[]',
                                },
                                allocationId: String(allocation._id),
                                displaySort: Number(allocation._id),
                                orderDocumentLine: allocation.orderDocumentLine as unknown as BaseDocumentLine,
                                quantityTransferred: String(allocation.quantityTransferred),
                                isPersisted: true,
                                jsonSerialNumbers: allocation.serialNumbers
                                    ? JSON.stringify(allocation.serialNumbers)
                                    : '[]',
                            },
                            allocationMappedWithStockSearchResult,
                        );
                }
            });

            // 2nd loop : add stock results without any allocation
            if (otherStockRecords.length === 0) {
                this.allocations.value.forEach(searchResult => {
                    const correspondingAllocation = allocationMappedWithStockSearchResult.find(
                        allocation => allocation.stockId === searchResult.stockId,
                    );
                    if (!correspondingAllocation)
                        otherStockRecords.push({
                            ...searchResult,
                            displaySort: 999999999999,
                        } as unknown as StockDataInterfaces.StockAllocationDetailPageBinding);
                });
            }

            this.allocations.value = otherStockRecords.concat(
                allocationMappedWithStockSearchResult,
            ) as unknown as PartialCollectionValueWithIds<StockDataInterfaces.StockAllocationDetailPageBinding>[];
        }
        return this.queryParams.isReturningDetails && !this.queryParams.needAllocationProposal
            ? []
            : linesToBeSelectedOnInit;
    }

    /**
     * Loads the allocations grid if we have already allocation entry information
     */
    private async loadCreationAllocations(
        linesToBeSelectedOnInit: Array<string>,
        creationAllocations?: StockDataInterfaces.AllocationUpdates | null,
    ) {
        let linesToBeSelectedOnInitSet = linesToBeSelectedOnInit;
        if (creationAllocations && creationAllocations.length > 0) {
            const allocations = this.allocations.value;

            let serialNumbers: SerialNumber[] = [];
            // TODO: to be replaced by asyncArray as soon as it is available for pages
            const promises: Promise<any>[] = [];
            creationAllocations.forEach(allocation => {
                if (allocation.serialNumbers && allocation.serialNumbers.length) {
                    promises.push(
                        this.$.graph
                            .node('@sage/xtrem-stock-data/SerialNumber')
                            .query(
                                ui.queryUtils.edgesSelector(
                                    { _id: true, id: true, allocation: { _id: true } },
                                    {
                                        first: 500,
                                        filter: {
                                            _id: {
                                                _in: allocation.serialNumbers,
                                            },
                                        },
                                    },
                                ),
                            )
                            .execute()
                            .then(sns => {
                                serialNumbers = serialNumbers.concat(withoutEdges(sns) as SerialNumber[]);
                            }),
                    );
                }
            });
            await Promise.all(promises);

            creationAllocations.forEach(allocation => {
                let lineIndex = 0;
                const line = allocations.find((stockSearchResult, index) => {
                    lineIndex = index;
                    return Number(stockSearchResult.stockId) === Number(allocation.stockRecord);
                });
                if (line) {
                    const lineSerialNumbers: SerialNumber[] = [];
                    if (allocation.serialNumbers && allocation.serialNumbers.length) {
                        allocation.serialNumbers.forEach(sn => {
                            const sn2 = serialNumbers.find(sn1 => sn1._id === sn);
                            if (sn2) {
                                lineSerialNumbers.push(sn2);
                            }
                        });
                    }

                    const concat = (allocationSerialNumbers: string) => {
                        let quantityToAllocate = 0;
                        switch (allocation.action as unknown as AllocationUpdateAction | 'reject') {
                            case 'decrease':
                                quantityToAllocate = (line.quantityToAllocate || 0) - Number(allocation.quantity || 0);
                                return {
                                    ...line,
                                    jsonSerialNumbers: allocationSerialNumbers,
                                    quantityToAllocate,
                                    allocationPersistedData: {
                                        ...line.allocationPersistedData,
                                        quantityToAllocate: 0,
                                        stockRecord: allocation.stockRecord,
                                    },
                                };
                            case 'transfer':
                                quantityToAllocate =
                                    (line.quantityToAllocate || 0) + Number(allocation.quantityToTransfer || 0);
                                return {
                                    ...line,
                                    orderAllocationId: allocation.allocationRecord,
                                    orderDocumentLine: this.queryParams.orderDocumentLine,
                                    jsonSerialNumbers: JSON.stringify(
                                        JSON.parse(line.jsonSerialNumbers || '[]').concat(
                                            JSON.parse(allocationSerialNumbers),
                                        ),
                                    ),
                                    quantityToAllocate,
                                    // quantityFound: (line.quantityFound || 0) - quantityToAllocate,
                                    allocationPersistedData: {
                                        ...line.allocationPersistedData,
                                        quantityToAllocate: 0,
                                        stockRecord: allocation.stockRecord,
                                        allocationId: allocation.allocationRecord,
                                        quantityProposed: Number(allocation.quantityToTransfer),
                                        jsonSerialNumbers: allocationSerialNumbers,
                                    },
                                };
                            case 'reject':
                                return {
                                    ...line,
                                    orderDocumentLine: this.queryParams.orderDocumentLine,
                                    allocationPersistedData: {
                                        ...line.allocationPersistedData,
                                        quantityToAllocate: 0,
                                        stockRecord: allocation.stockRecord,
                                        allocationId: allocation.allocationRecord,
                                        quantityProposed:
                                            (line.allocationPersistedData?.quantityProposed || 0) +
                                            Number(allocation.quantity || 0),
                                        jsonSerialNumbers: allocationSerialNumbers,
                                    },
                                };
                            case 'delete':
                                return {
                                    ...line,
                                    quantityToAllocate: 0,
                                };
                            case 'increase':
                            case 'create':
                            default:
                                quantityToAllocate = (line.quantityToAllocate || 0) + Number(allocation.quantity || 0);
                                return {
                                    ...line,
                                    quantityToAllocate,
                                    jsonSerialNumbers: JSON.stringify(
                                        JSON.parse(line.jsonSerialNumbers || '[]').concat(
                                            JSON.parse(allocationSerialNumbers),
                                        ),
                                    ),
                                    allocationPersistedData: {
                                        ...line.allocationPersistedData,
                                        quantityToAllocate: 0,
                                        stockRecord: allocation.stockRecord,
                                    },
                                };
                        }
                    };

                    const preparedLine = concat(JSON.stringify(lineSerialNumbers));
                    if (
                        (preparedLine.quantityToAllocate || 0) > 0 &&
                        !linesToBeSelectedOnInit.find(id => id === line._id)
                    ) {
                        linesToBeSelectedOnInitSet.push(line._id);
                    } else if (preparedLine.quantityToAllocate === 0) {
                        linesToBeSelectedOnInitSet = linesToBeSelectedOnInit.filter(
                            gridLineId => gridLineId !== line._id,
                        );
                        this.allocations.unselectRecord(line._id);
                    }

                    allocations[lineIndex] = {
                        ...preparedLine,
                        allocationPersistedData: {
                            ...preparedLine.allocationPersistedData,
                            // quantityToAllocate: preparedLine.quantityToAllocate,
                        },
                    } as PartialCollectionValueWithIds<StockDataInterfaces.StockAllocationDetailPageBinding>;
                }
            });

            this.allocations.value = allocations;
        }
        return linesToBeSelectedOnInitSet;
    }

    /**
     * Shows or hides the additional grid for the serial number assignment with the data linked to the
     * selected row of the stock details grid. Called when a line in the stock details grid is selected or unselected
     * or just clicked
     */
    private loadSerialNumberRangesForStockDetailLine(
        isSelected: boolean,
        rowData: StockDataInterfaces.StockAllocationDetailPageBinding,
    ) {
        // additional grid for serial number assignment is only shown for selected stock detail rows with a quantity
        // greater than zero and when serial numbers are managed for the item and the serial number management is done
        // for issues and receipts
        this.stockDetailId = rowData._id;
        this.stockRecordId = rowData.stockId ?? '';
        this.allocationSerialNumbers.value = [];
        if (
            isSelected &&
            rowData.quantityToAllocate !== 0 &&
            this.item.value?.serialNumberManagement === 'managed' &&
            this.item.value?.serialNumberUsage === 'issueAndReceipt'
        ) {
            if (rowData.jsonSerialNumberRanges && rowData.jsonSerialNumberRanges !== '{}') {
                const serialNumberRanges: ExtractEdgesPartial<StockDataInterfaces.StockIssueDetailSerialNumberPageBinding>[] =
                    JSON.parse(rowData.jsonSerialNumberRanges as string);
                serialNumberRanges.forEach(serialNumberRange => {
                    this.allocationSerialNumbers.addOrUpdateRecordValue(serialNumberRange);
                });
            }
            this.allocationSerialNumbers.isHidden = false;
        } else {
            this.allocationSerialNumbers.isHidden = true;
            this.updateSerialNumberPropertiesForLine(isSelected, '{}');
        }
    }

    // updates the json property for the linked serial number ranges in the stock details grid
    // additionally set the correct status in the 'Serial number' number pill property
    private updateSerialNumberPropertiesForLine(isSelected: boolean, newValue: string) {
        const relatedStockDetailLine = this.allocations.getRecordValue(this.stockDetailId);
        if (relatedStockDetailLine) {
            relatedStockDetailLine.jsonSerialNumberRanges = newValue;
            if (newValue === '{}') relatedStockDetailLine.jsonSerialNumbers = '[]';

            // calculate the correct serial number status
            if (
                this.item.value?.serialNumberManagement === 'managed' &&
                this.item.value?.serialNumberUsage === 'issueAndReceipt' &&
                isSelected &&
                relatedStockDetailLine.quantityToAllocate !== 0
            ) {
                // calculate total quantity in serial number grid for stock detail line
                const totalQuantity: integer = this.allocationSerialNumbers.value.reduce(
                    (prev: number, detailSerialNumber) => prev + (+detailSerialNumber.quantity || 0),
                    0,
                );
                if (relatedStockDetailLine.quantityToAllocate === totalQuantity)
                    relatedStockDetailLine.serialNumberStatus = 'assigned';
                else {
                    relatedStockDetailLine.serialNumberStatus =
                        totalQuantity === 0 ? 'toBeAssigned' : 'partiallyAssigned';
                }
            } else relatedStockDetailLine.serialNumberStatus = undefined;
            this.allocations.setRecordValue(relatedStockDetailLine);
        }
    }

    /**
     * Calculates the quantity in stock unit and validates the page (mandatory fields)
     * @returns string array (promise of) with all the error messages
     */
    // eslint-disable-next-line require-await
    async validate(notify = false): Promise<string[]> {
        return MasterDataUtils.runCustomValidation(
            this,
            // eslint-disable-next-line require-await
            async (_check, isValidated) => {
                if (isValidated()) {
                    // TODO: Implement here custom validation in the same fashion as this snippet
                    // check(
                    //     this.stockDetails.value.length > 0,
                    //     ui.localize(
                    //         '@sage/xtrem-stock-data/pages__stock_receipt_detail_panel__notification__no_details_entered',
                    //         "You didn't entered any stock detail.",
                    //     ),
                    // );
                }
            },
            notify,
        );
    }

    private isUnderAllocated(val: number, rowData: StockDataInterfaces.StockAllocationDetailPageBinding): boolean {
        return (
            val +
                this.allocations.value
                    .filter(allocationFilter => allocationFilter._id !== rowData._id)
                    .reduce((total, allocation) => total + (allocation.quantityToAllocate || 0), 0) <
            (this.quantity.value || 0)
        );
    }

    private isOverAllocated(val: number, rowData: StockDataInterfaces.StockAllocationDetailPageBinding): boolean {
        return (
            val +
                this.allocations.value
                    .filter(allocationFilter => allocationFilter._id !== rowData._id)
                    .reduce((total, allocation) => total + (allocation.quantityToAllocate || 0), 0) >
            (this.quantity.value || 0)
        );
    }

    private filterLocation() {
        this.allocations.value = this.allocationValue;
        if (this.defaultLocation.value && this.locationFilter.value !== 'allLocations') {
            this.allocations.value = this.allocations.value.filter(
                allocation =>
                    allocation.location?.id === this.defaultLocation.value?.id ||
                    (allocation.quantityToAllocate ?? 0) > 0,
            );
        }
    }
}
